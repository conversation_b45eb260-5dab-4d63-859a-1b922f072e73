"""
VP Test Tool 啟動畫面模組
提供美觀的啟動畫面和載入進度指示
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
from pathlib import Path
from PIL import Image, ImageTk
import logging

logger = logging.getLogger(__name__)

class SplashScreen:
    """啟動畫面類"""
    
    def __init__(self, title="VP Test Tool", version="V2.6.3"):
        """初始化啟動畫面"""
        self.title = title
        self.version = version
        self.splash = None
        self.progress_var = None
        self.status_var = None
        self.progress_bar = None
        self.is_closed = False
        
        # 創建啟動畫面
        self._create_splash_window()
        
    def _create_splash_window(self):
        """創建啟動畫面視窗"""
        try:
            # 創建頂層視窗
            self.splash = tk.Toplevel()
            self.splash.title("")
            self.splash.overrideredirect(True)  # 移除視窗邊框
            self.splash.configure(bg='white')
            
            # 設定視窗大小和位置
            width = 400
            height = 300
            screen_width = self.splash.winfo_screenwidth()
            screen_height = self.splash.winfo_screenheight()
            x = (screen_width - width) // 2
            y = (screen_height - height) // 2
            self.splash.geometry(f"{width}x{height}+{x}+{y}")
            
            # 設定視窗置頂
            self.splash.attributes('-topmost', True)
            
            # 創建主框架
            main_frame = tk.Frame(self.splash, bg='white', relief='raised', bd=2)
            main_frame.pack(fill='both', expand=True, padx=2, pady=2)
            
            # 嘗試載入應用程式圖示
            self._load_app_icon(main_frame)
            
            # 應用程式標題
            title_label = tk.Label(
                main_frame,
                text=self.title,
                font=('Arial', 20, 'bold'),
                bg='white',
                fg='#2c3e50'
            )
            title_label.pack(pady=(20, 5))
            
            # 版本信息
            version_label = tk.Label(
                main_frame,
                text=self.version,
                font=('Arial', 12),
                bg='white',
                fg='#7f8c8d'
            )
            version_label.pack(pady=(0, 20))
            
            # 載入狀態文字
            self.status_var = tk.StringVar()
            self.status_var.set("正在初始化...")
            status_label = tk.Label(
                main_frame,
                textvariable=self.status_var,
                font=('Arial', 10),
                bg='white',
                fg='#34495e'
            )
            status_label.pack(pady=(0, 10))
            
            # 進度條
            self.progress_var = tk.DoubleVar()
            self.progress_bar = ttk.Progressbar(
                main_frame,
                variable=self.progress_var,
                maximum=100,
                length=300,
                mode='determinate'
            )
            self.progress_bar.pack(pady=(0, 20))
            
            # 版權信息
            copyright_label = tk.Label(
                main_frame,
                text="© 2025 VP Test Tool 開發團隊",
                font=('Arial', 8),
                bg='white',
                fg='#95a5a6'
            )
            copyright_label.pack(side='bottom', pady=(0, 10))
            
            # 載入提示
            tip_label = tk.Label(
                main_frame,
                text="正在載入應用程式，請稍候...",
                font=('Arial', 9),
                bg='white',
                fg='#7f8c8d'
            )
            tip_label.pack(side='bottom', pady=(0, 5))
            
            # 更新視窗
            self.splash.update()
            
            logger.info("啟動畫面創建成功")
            
        except Exception as e:
            logger.error(f"創建啟動畫面失敗: {e}")
            self.splash = None
    
    def _load_app_icon(self, parent):
        """載入應用程式圖示"""
        try:
            # 嘗試載入圖示文件
            icon_paths = [
                "assets/app_icon.png",
                "assets/icons/vp_test_tool.ico",
                "assets/app_icon.ico"
            ]
            
            for icon_path in icon_paths:
                if Path(icon_path).exists():
                    try:
                        # 載入圖片
                        image = Image.open(icon_path)
                        # 調整大小
                        image = image.resize((64, 64), Image.Resampling.LANCZOS)
                        # 轉換為 PhotoImage
                        photo = ImageTk.PhotoImage(image)
                        
                        # 創建圖示標籤
                        icon_label = tk.Label(
                            parent,
                            image=photo,
                            bg='white'
                        )
                        icon_label.image = photo  # 保持引用
                        icon_label.pack(pady=(20, 10))
                        
                        logger.info(f"成功載入應用程式圖示: {icon_path}")
                        return
                        
                    except Exception as e:
                        logger.warning(f"載入圖示 {icon_path} 失敗: {e}")
                        continue
            
            # 如果沒有找到圖示，創建文字圖示
            icon_label = tk.Label(
                parent,
                text="VP",
                font=('Arial', 24, 'bold'),
                bg='#3498db',
                fg='white',
                width=4,
                height=2
            )
            icon_label.pack(pady=(20, 10))
            logger.info("使用文字圖示")
            
        except Exception as e:
            logger.warning(f"載入應用程式圖示失敗: {e}")
    
    def update_progress(self, progress, status=""):
        """更新進度"""
        try:
            if self.splash and not self.is_closed:
                if self.progress_var:
                    self.progress_var.set(progress)
                if self.status_var and status:
                    self.status_var.set(status)
                self.splash.update()
                
        except Exception as e:
            logger.warning(f"更新進度失敗: {e}")
    
    def close(self):
        """關閉啟動畫面"""
        try:
            if self.splash and not self.is_closed:
                self.is_closed = True
                self.splash.destroy()
                self.splash = None
                logger.info("啟動畫面已關閉")
                
        except Exception as e:
            logger.warning(f"關閉啟動畫面失敗: {e}")
    
    def is_valid(self):
        """檢查啟動畫面是否有效"""
        try:
            return self.splash is not None and not self.is_closed and self.splash.winfo_exists()
        except:
            return False

class ProgressTracker:
    """進度追蹤器"""
    
    def __init__(self, splash_screen, total_steps):
        """初始化進度追蹤器"""
        self.splash_screen = splash_screen
        self.total_steps = total_steps
        self.current_step = 0
        self.step_names = {}
        
    def add_step(self, step_name):
        """添加步驟"""
        self.step_names[self.current_step] = step_name
        
    def next_step(self, step_name=""):
        """進入下一步驟"""
        if step_name:
            self.step_names[self.current_step] = step_name
            
        progress = (self.current_step / self.total_steps) * 100
        status = self.step_names.get(self.current_step, f"步驟 {self.current_step + 1}")
        
        if self.splash_screen:
            self.splash_screen.update_progress(progress, status)
            
        self.current_step += 1
        
        # 添加小延遲讓用戶看到進度
        time.sleep(0.1)
        
    def complete(self):
        """完成所有步驟"""
        if self.splash_screen:
            self.splash_screen.update_progress(100, "載入完成")
            time.sleep(0.5)  # 顯示完成狀態
            self.splash_screen.close()

def show_splash_with_progress(init_function, init_steps):
    """顯示啟動畫面並執行初始化"""
    splash = None
    try:
        # 創建啟動畫面
        splash = SplashScreen()
        
        if not splash.is_valid():
            logger.warning("啟動畫面創建失敗，直接執行初始化")
            return init_function()
        
        # 創建進度追蹤器
        tracker = ProgressTracker(splash, len(init_steps))
        
        # 執行初始化步驟
        result = None
        for i, (step_name, step_function) in enumerate(init_steps):
            try:
                tracker.next_step(step_name)
                
                # 執行步驟
                if i == 0:  # 第一個步驟通常是主初始化函數
                    result = step_function()
                else:
                    step_function()
                    
            except Exception as e:
                logger.error(f"執行初始化步驟 '{step_name}' 失敗: {e}")
                if splash:
                    splash.update_progress((i / len(init_steps)) * 100, f"錯誤: {step_name}")
                    time.sleep(1)
                raise
        
        # 完成初始化
        tracker.complete()
        return result
        
    except Exception as e:
        logger.error(f"啟動畫面初始化失敗: {e}")
        if splash:
            splash.close()
        raise
    finally:
        # 確保啟動畫面被關閉
        if splash:
            splash.close()

def create_simple_splash():
    """創建簡單的啟動畫面（不帶進度）"""
    try:
        splash = SplashScreen()
        if splash.is_valid():
            splash.update_progress(0, "正在啟動...")
            return splash
        return None
    except Exception as e:
        logger.error(f"創建簡單啟動畫面失敗: {e}")
        return None
