"""版本管理模塊

此模塊集中管理應用程式的版本信息，所有需要使用版本號的地方都應該從這裡導入。
"""

# 主版本號
MAJOR = 2
# 次版本號
MINOR = 6
# 修訂版本號
PATCH = 3

# 完整版本號
VERSION = f"{MAJOR}.{MINOR}.{PATCH}"
# 應用程式標題
APP_TITLE = f"VP Test Tool V{VERSION}"
# 版本發布日期 (YYYY-MM-DD)
RELEASE_DATE = "2025-05-28"

# 版本描述
VERSION_DESCRIPTION = """
VP Test Tool V{VERSION} 版本更新 - 穩定版發布與版本管理：

📦 穩定版發布：
1. 完整打包系統：
   - 成功完成 V2.6.2 穩定版打包
   - 使用 cx_Freeze 8.1.0 進行可靠打包
   - 包含所有必要依賴庫和資源文件
   - 通過完整的功能驗證測試

🔢 版本管理優化：
2. 版本號跳躍更新：
   - 從 V2.6.2 跳版到 V2.6.3
   - 統一版本管理機制
   - 確保版本號一致性
   - 準備下一階段開發

📋 打包驗證：
3. 完整性檢查：
   - 主程式 VP_Test_Tool.exe 正常生成
   - 所有依賴庫正確打包 (103.6 MB)
   - 配置文件和資源文件完整
   - Visual C++ 運行時包含

🚀 部署準備：
4. 分發就緒：
   - 創建詳細的打包報告文檔
   - 提供完整的安裝和部署指南
   - 準備用戶分發包
   - 建立技術支援文檔

📚 文件完善：
5. 新增打包相關文檔：
   - STABLE_BUILD_V2.6.2_REPORT.md
   - PACKAGING_COMPLETE_V2.6.2.md
   - 詳細的故障排除指南
""".format(VERSION=VERSION)

def get_version_info():
    """獲取版本信息

    Returns:
        dict: 包含版本信息的字典
    """
    return {
        "major": MAJOR,
        "minor": MINOR,
        "patch": PATCH,
        "version": VERSION,
        "app_title": APP_TITLE,
        "release_date": RELEASE_DATE,
        "description": VERSION_DESCRIPTION
    }

def is_newer_version(version_to_check):
    """檢查指定的版本是否比當前版本更新

    Args:
        version_to_check (str): 要檢查的版本號，格式為 "x.y.z"

    Returns:
        bool: 如果指定的版本比當前版本更新，則返回 True，否則返回 False
    """
    try:
        # 解析版本號
        major, minor, patch = map(int, version_to_check.split('.'))

        # 比較版本號
        if major > MAJOR:
            return True
        if major == MAJOR and minor > MINOR:
            return True
        if major == MAJOR and minor == MINOR and patch > PATCH:
            return True

        return False
    except:
        # 如果解析失敗，返回 False
        return False

def get_version_string(include_v=True):
    """獲取版本號字符串

    Args:
        include_v (bool): 是否包含 "V" 前綴

    Returns:
        str: 版本號字符串
    """
    if include_v:
        return f"V{VERSION}"
    else:
        return VERSION

if __name__ == "__main__":
    # 測試代碼
    print(f"當前版本: {VERSION}")
    print(f"應用程式標題: {APP_TITLE}")
    print(f"版本發布日期: {RELEASE_DATE}")
    print(f"版本描述: {VERSION_DESCRIPTION}")

    # 測試版本比較
    test_versions = ["2.4.0", "2.5.0", "2.5.1", "2.6.0", "3.0.0"]
    for test_version in test_versions:
        print(f"{test_version} 是否比當前版本更新: {is_newer_version(test_version)}")
