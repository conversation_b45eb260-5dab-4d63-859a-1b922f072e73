2025-05-29 09:44:15,341 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - 成功創建實例鎖定文件: C:\Users\<USER>\AppData\Local\Temp\vp_test_tool.lock
2025-05-29 09:44:15,343 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - 使用啟動畫面模式進行初始化
2025-05-29 09:44:16,580 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-29 09:44:16,701 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-29 09:44:16,702 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-29 09:44:16,702 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-29 09:44:16,707 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 46134.28 MB, 使用率 29.4%
2025-05-29 09:44:16,707 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 39.85 GB, 使用率 95.7%
2025-05-29 09:44:16,844 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-29 09:44:16,950 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-29 09:44:17,056 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-29 09:44:17,292 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-29 09:44:17,293 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-29 09:44:19,239 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-29 09:44:19,700 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-29 09:44:19,806 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-29 09:44:19,806 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-29 09:44:19,908 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-29 09:44:19,908 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-29 09:45:30,918 - ERROR - 30812 - MainThread - app - enhanced_logger.py:278 - 初始化遊戲卡片工具控制器失敗: invalid command name ".!notebook.!memberpanel.!frame.!frame2.!cardframe.!frame.!frame2.!frame.!scrolledtext"
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\controllers\member_controller.py", line 365, in _load_game_list
    self.view.log(f"✅ 回應碼: {games_details['status_code']}")
  File "d:\Gitlab\VP_Test_Tool\views\member_panel.py", line 374, in log
    self.debug_text.insert(tk.END, f"{message}\n")
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 3809, in insert
    self.tk.call((self._w, 'insert', index, chars) + args)
_tkinter.TclError: invalid command name ".!notebook.!memberpanel.!frame.!frame2.!cardframe.!frame.!frame2.!frame.!scrolledtext"

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\controllers\member_controller.py", line 130, in _init_data
    self._load_game_list()
  File "d:\Gitlab\VP_Test_Tool\controllers\member_controller.py", line 386, in _load_game_list
    self.view.log(f"❌ 載入遊戲列表失敗: {str(e)}")
  File "d:\Gitlab\VP_Test_Tool\views\member_panel.py", line 374, in log
    self.debug_text.insert(tk.END, f"{message}\n")
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 3809, in insert
    self.tk.call((self._w, 'insert', index, chars) + args)
_tkinter.TclError: invalid command name ".!notebook.!memberpanel.!frame.!frame2.!cardframe.!frame.!frame2.!frame.!scrolledtext"

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 471, in _init_controllers
    member_controller = MemberController(
                        ^^^^^^^^^^^^^^^^^
  File "d:\Gitlab\VP_Test_Tool\controllers\member_controller.py", line 40, in __init__
    self._init_data()
  File "d:\Gitlab\VP_Test_Tool\controllers\member_controller.py", line 137, in _init_data
    self.view.show_error("初始化失敗", str(e))
  File "d:\Gitlab\VP_Test_Tool\views\member_panel.py", line 402, in show_error
    self.log(f"❌ 錯誤: {message}")
  File "d:\Gitlab\VP_Test_Tool\views\member_panel.py", line 374, in log
    self.debug_text.insert(tk.END, f"{message}\n")
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 3809, in insert
    self.tk.call((self._w, 'insert', index, chars) + args)
_tkinter.TclError: invalid command name ".!notebook.!memberpanel.!frame.!frame2.!cardframe.!frame.!frame2.!frame.!scrolledtext"
2025-05-29 09:45:30,949 - ERROR - 30812 - MainThread - app - enhanced_logger.py:278 - 初始化資源調整工具控制器失敗: invalid command name ".!notebook.!resourcepanel.!frame.!frame.!cardframe.!frame.!frame2.!frame.!modernbutton"
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 482, in _init_controllers
    resource_controller = ResourceController(
                          ^^^^^^^^^^^^^^^^^^^
  File "d:\Gitlab\VP_Test_Tool\controllers\resource_controller.py", line 43, in __init__
    self._init_bindings()
  File "d:\Gitlab\VP_Test_Tool\controllers\resource_controller.py", line 51, in _init_bindings
    self.view.btn_query.config(command=self._handle_query)
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1702, in configure
    return self._configure('configure', cnf, kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1692, in _configure
    self.tk.call(_flatten((self._w, cmd)) + self._options(cnf))
_tkinter.TclError: invalid command name ".!notebook.!resourcepanel.!frame.!frame.!cardframe.!frame.!frame2.!frame.!modernbutton"
2025-05-29 09:45:30,950 - ERROR - 30812 - MainThread - app - enhanced_logger.py:278 - 初始化帳號產生器控制器失敗: invalid command name ".!notebook.!accountpanel.!frame.!frame.!frame.!cardframe.!frame.!frame2.!frame.!frame2.!modernbutton"
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 493, in _init_controllers
    account_controller = AccountController(
                         ^^^^^^^^^^^^^^^^^^
  File "d:\Gitlab\VP_Test_Tool\controllers\account_controller.py", line 39, in __init__
    self._init_bindings()
  File "d:\Gitlab\VP_Test_Tool\controllers\account_controller.py", line 48, in _init_bindings
    self.view.btn_reset.config(command=self.reset_defaults)
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1702, in configure
    return self._configure('configure', cnf, kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1692, in _configure
    self.tk.call(_flatten((self._w, cmd)) + self._options(cnf))
_tkinter.TclError: invalid command name ".!notebook.!accountpanel.!frame.!frame.!frame.!cardframe.!frame.!frame2.!frame.!frame2.!modernbutton"
2025-05-29 09:45:30,952 - ERROR - 30812 - MainThread - app - enhanced_logger.py:278 - 初始化 RNG 控制器失敗: invalid command name ".!notebook.!rngpanel.!frame.!frame.!cardframe2.!frame.!frame2.!frame2.!frame.!modernbutton"
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 504, in _init_controllers
    rng_controller = RNGController(
                     ^^^^^^^^^^^^^^
  File "d:\Gitlab\VP_Test_Tool\controllers\rng_controller.py", line 32, in __init__
    self._init_bindings()
  File "d:\Gitlab\VP_Test_Tool\controllers\rng_controller.py", line 37, in _init_bindings
    self.view.btn_query.config(command=self._handle_query)
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1702, in configure
    return self._configure('configure', cnf, kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1692, in _configure
    self.tk.call(_flatten((self._w, cmd)) + self._options(cnf))
_tkinter.TclError: invalid command name ".!notebook.!rngpanel.!frame.!frame.!cardframe2.!frame.!frame2.!frame2.!frame.!modernbutton"
2025-05-29 09:45:31,073 - ERROR - 30812 - MainThread - app - enhanced_logger.py:278 - 初始化 API IP 切換工具控制器失敗: invalid command name ".!notebook.!ipswitcherpanel.!labelframe.!frame2.!button"
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 517, in _init_controllers
    ip_switcher_controller = IPSwitcherController(main_window.ip_switcher_panel)
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Gitlab\VP_Test_Tool\controllers\ip_switcher_controller.py", line 23, in __init__
    self.setup_bindings()
  File "d:\Gitlab\VP_Test_Tool\controllers\ip_switcher_controller.py", line 51, in setup_bindings
    self.view.switch_btn.config(command=self.quick_switch_ip)
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1702, in configure
    return self._configure('configure', cnf, kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1692, in _configure
    self.tk.call(_flatten((self._w, cmd)) + self._options(cnf))
_tkinter.TclError: invalid command name ".!notebook.!ipswitcherpanel.!labelframe.!frame2.!button"
2025-05-29 09:45:31,178 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-29 09:45:31,840 - INFO - 30812 - ThreadPoolExecutor-0_0 - app - enhanced_logger.py:234 - API 連接測試成功: mysql_operator - http://************:5000
2025-05-29 09:45:32,043 - INFO - 30812 - ThreadPoolExecutor-0_1 - app - enhanced_logger.py:234 - API 連接測試成功: gamebridge - http://gamebridge:8080
2025-05-29 09:45:32,044 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - API 連接檢測完成: 2/2 個服務連接正常
2025-05-29 09:45:32,051 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-05-29 09:45:32,052 - WARNING - 30812 - MainThread - app - enhanced_logger.py:245 - 初始化鍵盤快捷鍵管理器失敗: can't invoke "winfo" command: application has been destroyed
2025-05-29 09:45:32,052 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-05-29 09:45:32,053 - WARNING - 30812 - MainThread - app - enhanced_logger.py:245 - 顯示歡迎訊息失敗: invalid command name ".!label"
2025-05-29 09:45:32,054 - WARNING - 30812 - MainThread - app - enhanced_logger.py:245 - 添加功能檢測按鈕失敗: can't invoke "winfo" command: application has been destroyed
2025-05-29 09:45:32,054 - ERROR - 30812 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 1106, in main
    if root and root.winfo_exists():
                ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1139, in winfo_exists
    self.tk.call('winfo', 'exists', self._w))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
2025-05-29 09:45:32,055 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - 已清理實例鎖定文件
2025-05-29 09:45:32,056 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - 已停止資源監控
2025-05-29 09:45:33,069 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-05-29 09:45:34,080 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-05-29 09:45:34,084 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-05-29 09:45:35,904 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - 已停止資源監控
2025-05-29 09:45:35,904 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-05-29 09:45:35,905 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-05-29 09:45:35,905 - INFO - 30812 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-05-29 09:45:41,115 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 成功創建實例鎖定文件: C:\Users\<USER>\AppData\Local\Temp\vp_test_tool.lock
2025-05-29 09:45:41,116 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 使用啟動畫面模式進行初始化
2025-05-29 09:45:41,614 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-29 09:45:41,733 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-29 09:45:41,733 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-29 09:45:41,733 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-29 09:45:41,738 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 46074.09 MB, 使用率 29.5%
2025-05-29 09:45:41,738 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 39.85 GB, 使用率 95.7%
2025-05-29 09:45:41,851 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-29 09:45:41,954 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-29 09:45:42,060 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-29 09:45:42,270 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-29 09:45:42,270 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-29 09:45:43,858 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-29 09:45:44,286 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-29 09:45:44,390 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-29 09:45:44,390 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-29 09:45:44,493 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-29 09:45:44,493 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-29 09:46:23,780 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-29 09:46:23,780 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-29 09:46:24,618 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-29 09:46:24,619 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-29 09:46:24,626 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 成功初始化 API IP 切換工具控制器
2025-05-29 09:46:24,802 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-29 09:46:25,437 - INFO - 21744 - ThreadPoolExecutor-0_0 - app - enhanced_logger.py:234 - API 連接測試成功: mysql_operator - http://************:5000
2025-05-29 09:46:25,678 - INFO - 21744 - ThreadPoolExecutor-0_1 - app - enhanced_logger.py:234 - API 連接測試成功: gamebridge - http://gamebridge:8080
2025-05-29 09:46:25,679 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - API 連接檢測完成: 2/2 個服務連接正常
2025-05-29 09:46:25,697 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-05-29 09:46:25,707 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 成功初始化鍵盤快捷鍵管理器
2025-05-29 09:46:25,708 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-05-29 09:46:25,713 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 成功添加功能檢測按鈕
2025-05-29 09:49:53,743 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 應用程式正在關閉...
2025-05-29 09:49:53,744 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 已清理實例鎖定文件
2025-05-29 09:49:53,744 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 已停止資源監控
2025-05-29 09:49:54,757 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-05-29 09:49:55,775 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-05-29 09:49:55,775 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-05-29 09:49:55,802 - WARNING - 21744 - MainThread - app - enhanced_logger.py:245 - 關閉視窗時發生異常: can't delete Tcl command
2025-05-29 09:49:55,803 - INFO - 21744 - MainThread - app - enhanced_logger.py:234 - 強制退出程式
2025-05-29 09:51:26,701 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 成功創建實例鎖定文件: C:\Users\<USER>\AppData\Local\Temp\vp_test_tool.lock
2025-05-29 09:51:26,702 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 使用啟動畫面模式進行初始化
2025-05-29 09:51:27,139 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-29 09:51:27,259 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-29 09:51:27,260 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-29 09:51:27,260 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-29 09:51:27,265 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 45699.69 MB, 使用率 30.0%
2025-05-29 09:51:27,266 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 39.85 GB, 使用率 95.7%
2025-05-29 09:51:27,380 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-29 09:51:27,483 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-29 09:51:27,589 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-29 09:51:29,609 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 主視窗初始化完成（從啟動畫面轉換）
2025-05-29 09:51:29,721 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-29 09:51:29,823 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-29 09:51:29,823 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-29 09:51:29,924 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-29 09:51:29,924 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-29 09:51:31,148 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-29 09:51:31,149 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-29 09:51:31,471 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-29 09:51:31,472 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-29 09:51:31,476 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 成功初始化 API IP 切換工具控制器
2025-05-29 09:51:31,579 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-29 09:51:32,329 - INFO - 12304 - ThreadPoolExecutor-0_0 - app - enhanced_logger.py:234 - API 連接測試成功: mysql_operator - http://************:5000
2025-05-29 09:51:32,567 - INFO - 12304 - ThreadPoolExecutor-0_1 - app - enhanced_logger.py:234 - API 連接測試成功: gamebridge - http://gamebridge:8080
2025-05-29 09:51:32,568 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - API 連接檢測完成: 2/2 個服務連接正常
2025-05-29 09:51:32,580 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-05-29 09:51:32,580 - WARNING - 12304 - MainThread - app - enhanced_logger.py:245 - 初始化鍵盤快捷鍵管理器失敗: can't invoke "winfo" command: application has been destroyed
2025-05-29 09:51:32,580 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-05-29 09:51:32,580 - WARNING - 12304 - MainThread - app - enhanced_logger.py:245 - 顯示歡迎訊息失敗: invalid command name ".!label"
2025-05-29 09:51:32,580 - WARNING - 12304 - MainThread - app - enhanced_logger.py:245 - 添加功能檢測按鈕失敗: can't invoke "winfo" command: application has been destroyed
2025-05-29 09:51:32,580 - ERROR - 12304 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 1145, in main
    if root and root.winfo_exists():
                ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1139, in winfo_exists
    self.tk.call('winfo', 'exists', self._w))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
2025-05-29 09:51:32,587 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 已清理實例鎖定文件
2025-05-29 09:51:32,587 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 已停止資源監控
2025-05-29 09:51:33,591 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-05-29 09:51:34,609 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-05-29 09:51:34,609 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-05-29 09:52:44,163 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 已停止資源監控
2025-05-29 09:52:44,164 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-05-29 09:52:44,165 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-05-29 09:52:44,165 - INFO - 12304 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-05-29 09:56:04,475 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功創建實例鎖定文件: C:\Users\<USER>\AppData\Local\Temp\vp_test_tool.lock
2025-05-29 09:56:04,476 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 使用啟動畫面模式進行初始化
2025-05-29 09:56:04,966 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-29 09:56:05,086 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-29 09:56:05,086 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-29 09:56:05,086 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-29 09:56:05,091 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 45816.27 MB, 使用率 29.8%
2025-05-29 09:56:05,092 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 39.85 GB, 使用率 95.7%
2025-05-29 09:56:05,205 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-29 09:56:05,309 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-29 09:56:05,419 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-29 09:56:05,627 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-29 09:56:05,628 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-29 09:56:05,633 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-29 09:56:05,755 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-29 09:56:05,755 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-29 09:56:07,235 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-29 09:56:07,235 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-29 09:56:07,236 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-29 09:56:09,004 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-29 09:56:09,004 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-29 09:56:09,324 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-29 09:56:09,325 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-29 09:56:09,338 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功初始化 API IP 切換工具控制器
2025-05-29 09:56:09,471 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-29 09:56:10,096 - ERROR - 20316 - MainThread - app - enhanced_logger.py:256 - 啟動畫面無效，無法轉換
2025-05-29 09:56:10,097 - ERROR - 20316 - MainThread - app - enhanced_logger.py:256 - 啟動畫面初始化失敗: 視窗轉換失敗
2025-05-29 09:56:10,097 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 使用直接初始化模式
2025-05-29 09:56:10,098 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-29 09:56:10,098 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-29 09:56:10,098 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-29 09:56:10,099 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-29 09:56:10,102 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 45998.36 MB, 使用率 29.6%
2025-05-29 09:56:10,103 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 39.85 GB, 使用率 95.7%
2025-05-29 09:56:10,103 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-29 09:56:10,103 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-29 09:56:10,104 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-29 09:56:10,209 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-29 09:56:10,236 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-29 09:56:10,468 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-29 09:56:10,472 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-29 09:56:10,472 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-29 09:56:10,473 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-29 09:56:10,473 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-29 09:56:10,473 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-29 09:56:11,734 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-29 09:56:11,735 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-29 09:56:11,937 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-29 09:56:11,937 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-29 09:56:11,938 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功初始化 API IP 切換工具控制器
2025-05-29 09:56:11,938 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-29 09:56:11,949 - INFO - 20316 - ThreadPoolExecutor-0_0 - app - enhanced_logger.py:234 - API 連接測試成功: mysql_operator - http://************:5000
2025-05-29 09:56:12,211 - INFO - 20316 - ThreadPoolExecutor-0_1 - app - enhanced_logger.py:234 - API 連接測試成功: gamebridge - http://gamebridge:8080
2025-05-29 09:56:12,212 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - API 連接檢測完成: 2/2 個服務連接正常
2025-05-29 09:56:12,220 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-05-29 09:56:12,227 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功初始化鍵盤快捷鍵管理器
2025-05-29 09:56:12,227 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-05-29 09:56:12,230 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 成功添加功能檢測按鈕
2025-05-29 09:57:55,631 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 應用程式正在關閉...
2025-05-29 09:57:55,632 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 已清理實例鎖定文件
2025-05-29 09:57:55,632 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 已停止資源監控
2025-05-29 09:57:56,644 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-05-29 09:57:57,657 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-05-29 09:57:57,657 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-05-29 09:57:57,679 - WARNING - 20316 - MainThread - app - enhanced_logger.py:245 - 關閉視窗時發生異常: can't delete Tcl command
2025-05-29 09:57:57,680 - INFO - 20316 - MainThread - app - enhanced_logger.py:234 - 強制退出程式
2025-05-29 10:09:50,635 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 成功創建實例鎖定文件: C:\Users\<USER>\AppData\Local\Temp\vp_test_tool.lock
2025-05-29 10:09:50,635 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 使用啟動畫面模式進行初始化
2025-05-29 10:09:51,058 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-29 10:09:51,179 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-29 10:09:51,180 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-29 10:09:51,180 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-29 10:09:51,185 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 45577.18 MB, 使用率 30.2%
2025-05-29 10:09:51,185 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 39.85 GB, 使用率 95.7%
2025-05-29 10:09:51,299 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-29 10:09:51,402 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-29 10:09:51,509 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-29 10:09:51,714 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-29 10:09:51,714 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-29 10:09:51,740 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-29 10:09:51,860 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-29 10:09:51,860 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-29 10:09:53,455 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-29 10:09:53,455 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-29 10:09:53,456 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-29 10:09:54,943 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-29 10:09:54,944 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-29 10:09:55,271 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-29 10:09:55,271 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-29 10:09:55,276 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 成功初始化 API IP 切換工具控制器
2025-05-29 10:09:55,407 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-29 10:09:56,506 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 主視窗創建完成
2025-05-29 10:09:56,527 - INFO - 27480 - ThreadPoolExecutor-0_0 - app - enhanced_logger.py:234 - API 連接測試成功: mysql_operator - http://************:5000
2025-05-29 10:09:56,755 - INFO - 27480 - ThreadPoolExecutor-0_1 - app - enhanced_logger.py:234 - API 連接測試成功: gamebridge - http://gamebridge:8080
2025-05-29 10:09:56,755 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - API 連接檢測完成: 2/2 個服務連接正常
2025-05-29 10:09:56,766 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-05-29 10:09:56,776 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 成功初始化鍵盤快捷鍵管理器
2025-05-29 10:09:56,776 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-05-29 10:09:56,779 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 成功添加功能檢測按鈕
2025-05-29 10:12:08,666 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 應用程式正在關閉...
2025-05-29 10:12:08,667 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 已清理實例鎖定文件
2025-05-29 10:12:08,667 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 已停止資源監控
2025-05-29 10:12:09,675 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-05-29 10:12:10,684 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-05-29 10:12:10,684 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-05-29 10:12:10,816 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 視窗已關閉
2025-05-29 10:12:10,817 - INFO - 27480 - MainThread - app - enhanced_logger.py:234 - 強制退出程式
2025-05-29 10:14:34,799 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 成功創建實例鎖定文件: C:\Users\<USER>\AppData\Local\Temp\vp_test_tool.lock
2025-05-29 10:14:34,799 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 使用啟動畫面模式進行初始化
2025-05-29 10:14:35,241 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-29 10:14:35,360 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-29 10:14:35,361 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-29 10:14:35,361 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-29 10:14:35,366 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 45596.31 MB, 使用率 30.2%
2025-05-29 10:14:35,366 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 39.85 GB, 使用率 95.7%
2025-05-29 10:14:35,480 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-29 10:14:35,584 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-29 10:14:35,691 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-29 10:14:35,943 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-29 10:14:35,944 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-29 10:14:35,972 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-29 10:14:36,095 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-29 10:14:36,095 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-29 10:14:37,625 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-29 10:14:37,626 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-29 10:14:37,626 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-29 10:14:39,189 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-29 10:14:39,190 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-29 10:14:39,504 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-29 10:14:39,506 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-29 10:14:39,510 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 成功初始化 API IP 切換工具控制器
2025-05-29 10:14:39,633 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-29 10:14:40,252 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 正在關閉啟動畫面...
2025-05-29 10:14:40,479 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 正在創建主視窗...
2025-05-29 10:14:41,188 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 主視窗創建完成
2025-05-29 10:14:41,232 - INFO - 22204 - ThreadPoolExecutor-0_0 - app - enhanced_logger.py:234 - API 連接測試成功: mysql_operator - http://************:5000
2025-05-29 10:14:41,465 - INFO - 22204 - ThreadPoolExecutor-0_1 - app - enhanced_logger.py:234 - API 連接測試成功: gamebridge - http://gamebridge:8080
2025-05-29 10:14:41,468 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - API 連接檢測完成: 2/2 個服務連接正常
2025-05-29 10:14:41,482 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-05-29 10:14:41,493 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 成功初始化鍵盤快捷鍵管理器
2025-05-29 10:14:41,493 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-05-29 10:14:41,496 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 成功添加功能檢測按鈕
2025-05-29 10:18:31,463 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 程式已被使用者中斷
2025-05-29 10:18:31,464 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 已清理實例鎖定文件
2025-05-29 10:18:31,464 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 已停止資源監控
2025-05-29 10:18:32,474 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-05-29 10:18:33,494 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-05-29 10:18:33,494 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-05-29 10:18:33,495 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 已停止資源監控
2025-05-29 10:18:33,495 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-05-29 10:18:33,496 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-05-29 10:18:33,496 - INFO - 22204 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-05-29 10:19:50,461 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 成功創建實例鎖定文件: C:\Users\<USER>\AppData\Local\Temp\vp_test_tool.lock
2025-05-29 10:19:50,462 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 使用啟動畫面模式進行初始化
2025-05-29 10:19:50,853 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-29 10:19:50,988 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-29 10:19:50,988 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-29 10:19:50,989 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-29 10:19:50,993 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 45131.67 MB, 使用率 30.9%
2025-05-29 10:19:50,993 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 39.85 GB, 使用率 95.7%
2025-05-29 10:19:51,107 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-29 10:19:51,210 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-29 10:19:51,316 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-29 10:19:51,533 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-29 10:19:51,533 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-29 10:19:51,566 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-29 10:19:51,681 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-29 10:19:51,681 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-29 10:19:53,236 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-29 10:19:53,236 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-29 10:19:53,236 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-29 10:19:54,756 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-29 10:19:54,757 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-29 10:19:55,055 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-29 10:19:55,056 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-29 10:19:55,064 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 成功初始化 API IP 切換工具控制器
2025-05-29 10:19:55,194 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-29 10:19:55,833 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 正在關閉啟動畫面...
2025-05-29 10:19:56,368 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 啟動畫面關閉完成，準備創建主視窗
2025-05-29 10:19:56,368 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 正在創建主視窗...
2025-05-29 10:19:57,214 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 主視窗創建完成
2025-05-29 10:19:57,237 - INFO - 13176 - ThreadPoolExecutor-0_0 - app - enhanced_logger.py:234 - API 連接測試成功: mysql_operator - http://************:5000
2025-05-29 10:19:57,488 - INFO - 13176 - ThreadPoolExecutor-0_1 - app - enhanced_logger.py:234 - API 連接測試成功: gamebridge - http://gamebridge:8080
2025-05-29 10:19:57,488 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - API 連接檢測完成: 2/2 個服務連接正常
2025-05-29 10:19:57,519 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-05-29 10:19:57,533 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 成功初始化鍵盤快捷鍵管理器
2025-05-29 10:19:57,533 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-05-29 10:19:57,535 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 成功添加功能檢測按鈕
2025-05-29 10:20:34,306 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 應用程式正在關閉...
2025-05-29 10:20:34,307 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 已清理實例鎖定文件
2025-05-29 10:20:34,307 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 已停止資源監控
2025-05-29 10:20:35,312 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-05-29 10:20:36,320 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-05-29 10:20:36,320 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-05-29 10:20:36,442 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 視窗已關閉
2025-05-29 10:20:36,442 - INFO - 13176 - MainThread - app - enhanced_logger.py:234 - 強制退出程式
2025-05-29 10:20:40,628 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 成功創建實例鎖定文件: C:\Users\<USER>\AppData\Local\Temp\vp_test_tool.lock
2025-05-29 10:20:40,628 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 使用啟動畫面模式進行初始化
2025-05-29 10:20:41,069 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-29 10:20:41,191 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-29 10:20:41,191 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-29 10:20:41,192 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-29 10:20:41,197 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 45530.09 MB, 使用率 30.3%
2025-05-29 10:20:41,197 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 39.85 GB, 使用率 95.7%
2025-05-29 10:20:41,311 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-29 10:20:41,414 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-29 10:20:41,520 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-29 10:20:41,709 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-29 10:20:41,709 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-29 10:20:41,742 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-29 10:20:41,860 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-29 10:20:41,860 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-29 10:20:43,386 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-29 10:20:43,387 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-29 10:20:43,387 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-29 10:20:44,883 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-29 10:20:44,884 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-29 10:20:45,192 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-29 10:20:45,193 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-29 10:20:45,198 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 成功初始化 API IP 切換工具控制器
2025-05-29 10:20:45,326 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-29 10:20:45,946 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 正在關閉啟動畫面...
2025-05-29 10:20:46,488 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 啟動畫面關閉完成，準備創建主視窗
2025-05-29 10:20:46,489 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 正在創建主視窗...
2025-05-29 10:20:47,190 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 主視窗創建完成
2025-05-29 10:20:47,202 - INFO - 13300 - ThreadPoolExecutor-0_0 - app - enhanced_logger.py:234 - API 連接測試成功: mysql_operator - http://************:5000
2025-05-29 10:20:47,457 - INFO - 13300 - ThreadPoolExecutor-0_1 - app - enhanced_logger.py:234 - API 連接測試成功: gamebridge - http://gamebridge:8080
2025-05-29 10:20:47,458 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - API 連接檢測完成: 2/2 個服務連接正常
2025-05-29 10:20:47,468 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-05-29 10:20:47,471 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 成功初始化鍵盤快捷鍵管理器
2025-05-29 10:20:47,471 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-05-29 10:20:47,473 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 成功添加功能檢測按鈕
2025-05-29 10:20:58,905 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 應用程式正在關閉...
2025-05-29 10:20:58,906 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 已清理實例鎖定文件
2025-05-29 10:20:58,906 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 已停止資源監控
2025-05-29 10:20:59,910 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-05-29 10:21:00,921 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-05-29 10:21:00,921 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-05-29 10:21:01,020 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 視窗已關閉
2025-05-29 10:21:01,020 - INFO - 13300 - MainThread - app - enhanced_logger.py:234 - 強制退出程式
