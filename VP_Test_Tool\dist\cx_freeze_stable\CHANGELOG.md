# VP Test Tool 更新日誌

## V2.6.3 (2025-05-28) - 穩定版發布與版本管理

### 穩定版發布
- **完整打包系統**: 成功完成 V2.6.2 穩定版打包，使用 cx_Freeze 8.1.0 進行可靠打包
- **依賴庫完整**: 包含所有必要依賴庫和資源文件 (總大小 103.6 MB)
- **功能驗證**: 通過完整的功能驗證測試，確保所有功能正常運作
- **部署準備**: 創建詳細的打包報告和安裝指南，準備用戶分發

### 版本管理優化
- **版本號跳躍**: 從 V2.6.2 跳版到 V2.6.3，統一版本管理機制
- **版本一致性**: 確保所有打包腳本和配置文件中的版本號一致
- **打包腳本更新**: 更新所有打包腳本 (setup_*.py) 中的版本號
- **文檔同步**: 同步更新相關文檔和配置文件

### 打包驗證與文檔
- **完整性檢查**: 主程式 VP_Test_Tool.exe 正常生成，所有依賴庫正確打包
- **配置文件**: 配置文件和資源文件完整保留，Visual C++ 運行時包含
- **技術文檔**: 新增 STABLE_BUILD_V2.6.2_REPORT.md 和 PACKAGING_COMPLETE_V2.6.2.md
- **故障排除**: 提供詳細的安裝指南和故障排除文檔

### 分發準備
- **發布包**: 準備完整的用戶分發包，包含所有必要文件
- **系統需求**: 明確系統需求 (Windows 10/11, 4GB+ RAM, Visual C++ 運行時)
- **安全建議**: 提供防毒軟體白名單建議，避免誤報問題
- **技術支援**: 建立完整的技術支援文檔和聯繫方式

## V2.6.2 (2025-05-28) - API IP 切換工具優化

### 功能修復與優化
- **API IP 切換工具**: 修復頁籤名稱顯示問題，統一為 "API IP 切換工具"
- **控制器初始化**: 修復控制器初始化問題，確保功能正常運作
- **界面優化**: 移除多餘的 network 圖示，簡化界面顯示
- **錯誤處理**: 完善錯誤處理和日誌記錄機制

### 測試與驗證
- **測試套件**: 新增完整測試套件，包含基本功能測試和操作功能測試
- **功能驗證**: 驗證模組導入、IP 切換邏輯、模板應用和配置驗證
- **歷史記錄**: 驗證操作歷史管理功能

### 除錯與診斷
- **除錯報告**: 新增詳細的除錯報告生成功能
- **狀態檢查**: 提供完整的功能狀態檢查
- **診斷工具**: 改進錯誤診斷和問題定位能力
- **自動化測試**: 新增自動化測試腳本

### UI 改進
- **頁籤命名**: 統一頁籤命名規範
- **導航提示**: 優化導航提示文字
- **快捷鍵**: 改進快捷鍵支援 (Ctrl+5)
- **圖示顯示**: 簡化圖示顯示

### 文件更新
- 新增 `API_IP_SWITCHER_DEBUG_REPORT.md` - 詳細的除錯報告
- 新增 `test_ip_switcher.py` - 基本功能測試腳本
- 新增 `test_ip_operations.py` - 操作功能測試腳本

## V2.6.1 (2025-05-28) - IP 管理系統

### 重大功能新增
- **IP 管理系統**: 新增完整的 IP 管理解決方案，解決頻繁 IP 變更的維護問題
  - **環境配置管理器**: 支援多環境配置（開發、測試、生產等），可輕鬆切換不同環境
  - **快速 IP 切換工具**: 支援批量 IP 替換、模板應用和歷史記錄恢復
  - **動態 API URLs**: 根據當前環境自動生成 API URLs，支援熱重載
  - **配置模板系統**: 預設多種環境模板，支援自訂模板管理
  - **備份與恢復**: 支援環境配置的備份和恢復功能
  - **配置驗證**: 自動驗證環境配置的完整性和正確性
  - **操作歷史**: 記錄所有 IP 切換操作，支援一鍵恢復

### UI 新增
- **環境管理界面**: 新增專用的環境管理頁籤，提供圖形化的環境配置管理
- **IP 切換工具界面**: 新增專用的 IP 切換工具頁籤，提供直觀的 IP 管理操作
- **快捷鍵支援**: 新增 Ctrl+5 (環境管理) 和 Ctrl+6 (IP 切換工具) 快捷鍵

### 架構改進
- **模組化設計**: 將 IP 管理功能設計為獨立模組，易於維護和擴展
- **配置檔案管理**: 使用 JSON 格式儲存環境配置，支援版本控制
- **錯誤處理增強**: 提供詳細的錯誤訊息和操作指導
- **日誌記錄**: 記錄所有重要操作，便於問題追蹤和診斷

### 文件更新
- 新增 `docs/IP_MANAGEMENT_GUIDE.md` - 完整的 IP 管理系統使用指南
- 新增 `test_ip_management.py` - IP 管理系統測試腳本
- 更新程式碼註釋和文件說明

### API 更新
- 將所有硬編碼的 API URL 替換為動態配置
- 統一使用 `utils.constants.API_URLS` 管理所有 API 端點
- 支援環境切換時自動更新 API URLs

## V2.5.2 (2025-05-13)

### 功能增強
- 無新增功能

### 錯誤修復
- 修復 Git 下載功能線程安全問題：解決 "main thread is not in main loop" 錯誤
- 修復 RNG 設置功能中的線程安全問題：改進 UI 更新機制

### 其他改進
- 改進 Git 下載功能的穩定性：添加超時處理和備用下載方法
- 改進 UI 更新機制：使用隊列進行線程間通信，避免線程安全問題

## V2.5.1 (2025-04-30)

### 功能增強
- 無新增功能

### 錯誤修復
- 修復帳號產生線程失敗問題：解決 "main thread is not in main loop" 錯誤
- 修復 MemberService.create_member() 參數錯誤：移除不支援的 password 參數

### 其他改進
- 改進帳號產生器的穩定性：使用主線程分批處理帳號，避免線程安全問題
- 優化錯誤處理機制：提供更詳細的錯誤訊息和日誌記錄

## V2.5.0 (2025-06-15)

### 功能增強
- 添加功能檢測頁面：新增專用頁面顯示系統中可用的功能，包括內存監控、網絡恢復等
- 添加自動更新功能：自動檢查和下載程序更新，提供更新通知和下載進度顯示
- 添加增強錯誤處理：提供更詳細的錯誤信息、解決方案建議和錯誤報告生成
- 添加安裝指南：提供詳細的安裝和設置指南，包括依賴庫安裝、環境配置和功能啟用說明

### UI 改進
- 優化功能檢測頁面：使用標籤頁分離可用和不可用功能，提供更清晰的功能狀態顯示
- 添加系統信息摘要：在功能檢測頁面顯示系統信息摘要，提供更多系統狀態信息
- 添加工具提示：為功能項目添加工具提示，顯示完整的功能信息
- 添加功能檢測按鈕：在主視窗添加功能檢測和更新檢查按鈕，方便用戶使用

### 依賴庫管理
- 更新 requirements.txt：添加所有依賴庫，包括基本依賴庫、打包工具、新功能依賴庫和可選依賴庫
- 添加依賴庫檢測：在程序啟動時檢測可用的依賴庫，提供更好的兼容性
- 添加模擬對象：在缺少依賴庫時使用模擬對象，確保程序能夠正常運行

## V2.4.0 (2025-05-20)

### 功能增強
- 內存優化：添加內存監控和優化功能，提高大量數據處理時的穩定性
- 網絡恢復：添加網絡連接問題的自動恢復功能，提高程序穩定性
- 用戶界面增強：添加進度條動畫和狀態圖標，提供更好的視覺反饋
- 日誌系統升級：增強日誌記錄系統，添加詳細的操作日誌和錯誤診斷
- 斷點續傳：添加文件下載的斷點續傳功能，提高大文件下載的穩定性
- 資源監控：添加系統資源使用顯示，實時監控內存和 CPU 使用情況
- 批量處理優化：改進批量處理的內存使用和錯誤恢復機制
- 錯誤診斷：添加網絡連接問題的診斷功能，提供更詳細的錯誤信息

## V2.3.1 (2025-05-05)

### 錯誤處理改進
- 批次處理時不再顯示彈出錯誤對話框，而是將錯誤訊息記錄到日誌中，並在批次處理結果中顯示
- 修改 HTTP 客戶端的錯誤處理機制，讓上層代碼能夠更好地處理錯誤
- 確保所有 API URL 使用正確的域名

## V2.3.0 (2025-04-28)

### UI 改進
- 會員資訊區域增強：在「資源調整工具」、「遊戲卡片工具」和「Slot Set RNG」頁籤中的會員資訊區域現在包含資料庫來源、平台帳號和 VP Member ID 欄位
- Slot Set RNG 頁面優化：
  - 將會員設定區域的查詢會員和清除資料按鈕從上下排序改為左右排序，並放置在區域的中間位置
  - 將遊戲設定區域的遊戲和 RNG 從上下排序改為左右排序
  - 減少會員設定區域三個欄位間的空白縮排
  - 將描述區域寬度調整為原比例的 60%
- 帳號產生器頁面改進：在 SubAgentCode 下方的進度條現在顯示綠色，並添加百分比顯示

### API 整合
- 資源調整工具更新：
  - 金幣更新 API 從 `http://10.10.104.33:5000/platform/resource/update-account-coin` 更改為 `http://gamebridge:8080/private/modifyBalance`
  - 更新 API 請求格式，使用 `agent`、`account` 和 `newBalance` 參數
  - 修改成功判斷條件，使用 `retStatus.StatusCode == 10000` 判斷成功
  - 金幣數量輸入欄位現在支援小數點

## V2.2.0 (2025-04-15)

### 功能更新
- 網絡連接問題時直接顯示錯誤訊息，避免程式凍結
- 增加網絡測試模式，可測試所有API端點連接狀態
- 減少HTTP請求超時時間，提高程式響應速度
- 改進錯誤處理機制，提供更詳細的錯誤訊息

### UI 改進
- 所有頁面中的「VP 會員 ID」標籤已修改為「VP Member ID」
- 所有功能頁面的左右區域寬度已設定為50/50比例
- 所有功能使用真實API資料，不使用模擬資料
