#!/usr/bin/env python3
"""
VP Test Tool - 自動化發布打包腳本

此腳本提供完整的自動化打包流程，包括：
- 環境檢查
- 依賴安裝
- 版本管理
- 多種打包方式
- 自動測試
- 發布準備

使用方法:
    python build_release.py --help
    python build_release.py --build-all
    python build_release.py --cx-freeze
    python build_release.py --test-only

版本: V2.6.3
作者: VP Test Tool 開發團隊
"""

import sys
import os
import argparse
import subprocess
import shutil
import json
import time
from pathlib import Path
from datetime import datetime

class ReleaseBuilder:
    """發布打包器"""

    def __init__(self):
        self.project_root = Path.cwd()
        self.version_info = self._load_version()
        self.build_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    def _load_version(self):
        """載入版本信息"""
        try:
            sys.path.insert(0, str(self.project_root))
            from utils.version import VERSION, APP_TITLE, RELEASE_DATE
            return {
                "version": VERSION,
                "title": APP_TITLE,
                "date": RELEASE_DATE
            }
        except ImportError:
            return {
                "version": "2.6.3",
                "title": "VP Test Tool V2.6.3",
                "date": datetime.now().strftime("%Y-%m-%d")
            }

    def print_header(self):
        """打印標題"""
        print("=" * 70)
        print(f"🚀 {self.version_info['title']} 自動化打包系統")
        print(f"📅 版本: {self.version_info['version']}")
        print(f"🕒 打包時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 70)

    def check_environment(self):
        """檢查環境"""
        print("\n🔍 檢查打包環境...")

        # 檢查 Python 版本
        if sys.version_info < (3, 8):
            print("❌ 需要 Python 3.8 或更高版本")
            return False
        print(f"✅ Python: {sys.version.split()[0]}")

        # 檢查必要文件
        required_files = [
            "main.py",
            "utils/version.py",
            "config.json",
            "setup_cx_freeze_stable.py"
        ]

        missing_files = []
        for file_path in required_files:
            if not Path(file_path).exists():
                missing_files.append(file_path)
            else:
                print(f"✅ {file_path}")

        if missing_files:
            print(f"❌ 缺少必要文件: {missing_files}")
            return False

        # 檢查依賴庫
        dependencies = [
            "cx_Freeze",
            "tkinter",
            "PIL",
            "requests",
            "pandas",
            "numpy",
            "openpyxl",
            "psutil"
        ]

        missing_deps = []
        for dep in dependencies:
            try:
                if dep == "cx_Freeze":
                    import cx_Freeze
                    print(f"✅ {dep}: {cx_Freeze.version}")
                elif dep == "tkinter":
                    import tkinter
                    print(f"✅ {dep}: 已安裝")
                elif dep == "PIL":
                    from PIL import Image
                    print(f"✅ {dep}: {Image.__version__}")
                else:
                    module = __import__(dep)
                    version = getattr(module, "__version__", "已安裝")
                    print(f"✅ {dep}: {version}")
            except ImportError:
                missing_deps.append(dep)
                print(f"❌ {dep}: 未安裝")

        if missing_deps:
            print(f"\n⚠️ 缺少依賴庫: {missing_deps}")
            print("💡 建議執行: pip install -r requirements.txt")
            return False

        print("✅ 環境檢查通過")
        return True

    def install_dependencies(self):
        """安裝依賴"""
        print("\n📦 安裝/更新依賴庫...")

        # 檢查 requirements.txt
        req_file = Path("requirements.txt")
        if req_file.exists():
            try:
                subprocess.run([
                    sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
                ], check=True)
                print("✅ 依賴庫安裝完成")
                return True
            except subprocess.CalledProcessError as e:
                print(f"❌ 依賴庫安裝失敗: {e}")
                return False
        else:
            print("⚠️ 未找到 requirements.txt，跳過依賴安裝")
            return True

    def clean_build_dirs(self):
        """清理打包目錄"""
        print("\n🧹 清理舊的打包文件...")

        dirs_to_clean = ["build", "dist", "__pycache__"]
        for dir_name in dirs_to_clean:
            dir_path = Path(dir_name)
            if dir_path.exists():
                shutil.rmtree(dir_path)
                print(f"✅ 清理: {dir_name}")

        # 清理 .pyc 文件
        for pyc_file in self.project_root.rglob("*.pyc"):
            pyc_file.unlink()

        print("✅ 清理完成")

    def build_cx_freeze(self):
        """使用 cx_Freeze 打包"""
        print("\n🔧 使用 cx_Freeze 打包...")

        try:
            # 執行穩定版打包腳本
            result = subprocess.run([
                sys.executable, "setup_cx_freeze_stable.py", "build"
            ], capture_output=True, text=True, timeout=600)

            if result.returncode == 0:
                print("✅ cx_Freeze 打包成功")
                print(result.stdout)
                return True
            else:
                print("❌ cx_Freeze 打包失敗")
                print(result.stderr)
                return False

        except subprocess.TimeoutExpired:
            print("❌ cx_Freeze 打包超時")
            return False
        except Exception as e:
            print(f"❌ cx_Freeze 打包異常: {e}")
            return False

    def test_build(self):
        """測試打包結果"""
        print("\n🧪 測試打包結果...")

        # 檢查是否有測試腳本
        test_script = Path("test_build.py")
        if test_script.exists():
            try:
                result = subprocess.run([
                    sys.executable, "test_build.py"
                ], capture_output=True, text=True, timeout=60)

                if result.returncode == 0:
                    print("✅ 打包測試通過")
                    print(result.stdout)
                    return True
                else:
                    print("❌ 打包測試失敗")
                    print(result.stderr)
                    return False
            except Exception as e:
                print(f"❌ 測試執行異常: {e}")
                return False
        else:
            print("⚠️ 未找到測試腳本，跳過測試")
            return True

    def create_release_package(self):
        """創建發布包"""
        print("\n📦 創建發布包...")

        version = self.version_info['version']
        release_name = f"VP_Test_Tool_V{version}_{self.build_timestamp}"

        # 創建發布目錄
        release_dir = Path("releases") / release_name
        release_dir.mkdir(parents=True, exist_ok=True)

        # 複製打包結果
        build_dirs = [
            ("dist/cx_freeze_stable", "cx_freeze"),
        ]

        for src, dst in build_dirs:
            src_path = Path(src)
            if src_path.exists():
                dst_path = release_dir / dst
                shutil.copytree(src_path, dst_path)
                print(f"✅ 複製: {src} -> {dst}")

        # 複製文檔
        docs = [
            "README.md",
            "CHANGELOG.md",
            "DEBUG_REPORT_V2.6.1.md",
            "requirements.txt"
        ]

        for doc in docs:
            doc_path = Path(doc)
            if doc_path.exists():
                shutil.copy2(doc_path, release_dir)
                print(f"✅ 複製文檔: {doc}")

        # 創建發布信息
        release_info = {
            "version": version,
            "title": self.version_info['title'],
            "build_date": datetime.now().isoformat(),
            "build_timestamp": self.build_timestamp,
            "python_version": sys.version,
            "platform": sys.platform,
            "files": {
                "cx_freeze": "cx_freeze/VP_Test_Tool.exe"
            }
        }

        with open(release_dir / "release_info.json", "w", encoding="utf-8") as f:
            json.dump(release_info, f, indent=2, ensure_ascii=False)

        print(f"✅ 發布包創建完成: {release_dir}")
        return release_dir

    def build_all(self):
        """完整打包流程"""
        print("\n🚀 開始完整打包流程...")

        steps = [
            ("環境檢查", self.check_environment),
            ("清理目錄", self.clean_build_dirs),
            ("cx_Freeze 打包", self.build_cx_freeze),
            ("測試打包", self.test_build),
            ("創建發布包", self.create_release_package),
        ]

        for step_name, step_func in steps:
            print(f"\n📋 執行步驟: {step_name}")
            if not step_func():
                print(f"❌ 步驟失敗: {step_name}")
                return False
            print(f"✅ 步驟完成: {step_name}")

        print(f"\n🎉 完整打包流程成功完成！")
        print(f"📁 發布包位置: releases/")
        return True

def main():
    """主函數"""
    parser = argparse.ArgumentParser(description="VP Test Tool 自動化打包系統")
    parser.add_argument("--build-all", action="store_true", help="執行完整打包流程")
    parser.add_argument("--cx-freeze", action="store_true", help="僅執行 cx_Freeze 打包")
    parser.add_argument("--test-only", action="store_true", help="僅執行測試")
    parser.add_argument("--clean", action="store_true", help="僅清理打包目錄")
    parser.add_argument("--check-env", action="store_true", help="僅檢查環境")
    parser.add_argument("--install-deps", action="store_true", help="安裝依賴庫")

    args = parser.parse_args()

    builder = ReleaseBuilder()
    builder.print_header()

    if args.check_env:
        builder.check_environment()
    elif args.install_deps:
        builder.install_dependencies()
    elif args.clean:
        builder.clean_build_dirs()
    elif args.cx_freeze:
        if builder.check_environment():
            builder.clean_build_dirs()
            builder.build_cx_freeze()
    elif args.test_only:
        builder.test_build()
    elif args.build_all:
        builder.build_all()
    else:
        print("\n💡 使用說明:")
        print("  python build_release.py --build-all     # 完整打包流程")
        print("  python build_release.py --cx-freeze     # 僅 cx_Freeze 打包")
        print("  python build_release.py --test-only     # 僅測試打包結果")
        print("  python build_release.py --check-env     # 檢查環境")
        print("  python build_release.py --install-deps  # 安裝依賴")
        print("  python build_release.py --clean         # 清理打包目錄")
        print("  python build_release.py --help          # 顯示幫助")

if __name__ == "__main__":
    main()
