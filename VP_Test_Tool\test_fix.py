#!/usr/bin/env python3
"""
VP Test Tool 修復測試腳本
測試多視窗問題修復和功能恢復
"""

import sys
import os
import time
import threading
import tkinter as tk
from pathlib import Path

# 添加專案路徑
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """測試模組導入"""
    print("🔍 測試模組導入...")
    
    try:
        # 測試核心模組
        from utils.config import Config
        print("✅ Config 模組導入成功")
        
        from views.main_window import MainWindow
        print("✅ MainWindow 模組導入成功")
        
        from models.member import MemberService
        print("✅ MemberService 模組導入成功")
        
        from models.agent import AgentService
        print("✅ AgentService 模組導入成功")
        
        # 測試控制器模組
        from controllers.member_controller import MemberController
        print("✅ MemberController 模組導入成功")
        
        from controllers.resource_controller import ResourceController
        print("✅ ResourceController 模組導入成功")
        
        from controllers.account_controller import AccountController
        print("✅ AccountController 模組導入成功")
        
        from controllers.rng_controller import RNGController
        print("✅ RNGController 模組導入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模組導入失敗: {e}")
        return False

def test_window_creation():
    """測試視窗創建"""
    print("\n🔍 測試視窗創建...")
    
    try:
        # 創建根視窗
        root = tk.Tk()
        root.title("測試視窗")
        root.geometry("400x300")
        
        # 創建配置
        from utils.config import Config
        config = Config()
        
        # 創建主視窗
        from views.main_window import MainWindow
        main_window = MainWindow(root, config)
        
        print("✅ 主視窗創建成功")
        
        # 檢查主視窗組件
        if hasattr(main_window, 'member_panel'):
            print("✅ member_panel 存在")
        else:
            print("❌ member_panel 不存在")
            
        if hasattr(main_window, 'resource_panel'):
            print("✅ resource_panel 存在")
        else:
            print("❌ resource_panel 不存在")
            
        if hasattr(main_window, 'account_panel'):
            print("✅ account_panel 存在")
        else:
            print("❌ account_panel 不存在")
            
        if hasattr(main_window, 'rng_panel'):
            print("✅ rng_panel 存在")
        else:
            print("❌ rng_panel 不存在")
        
        # 關閉視窗
        root.after(1000, root.quit)
        root.mainloop()
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ 視窗創建失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_controller_creation():
    """測試控制器創建"""
    print("\n🔍 測試控制器創建...")
    
    try:
        # 創建根視窗
        root = tk.Tk()
        root.title("控制器測試")
        root.geometry("400x300")
        
        # 創建配置
        from utils.config import Config
        config = Config()
        
        # 創建主視窗
        from views.main_window import MainWindow
        main_window = MainWindow(root, config)
        
        # 創建服務
        from utils.http_client_enhanced import http_client_enhanced
        from models.member import MemberService
        from models.agent import AgentService
        
        member_service = MemberService(http_client_enhanced)
        agent_service = AgentService(http_client_enhanced)
        
        print("✅ 服務創建成功")
        
        # 測試控制器創建
        controllers = []
        
        try:
            from controllers.member_controller import MemberController
            member_controller = MemberController(
                main_window.member_panel,
                member_service,
                agent_service
            )
            controllers.append(member_controller)
            print("✅ MemberController 創建成功")
        except Exception as e:
            print(f"❌ MemberController 創建失敗: {e}")
        
        try:
            from controllers.resource_controller import ResourceController
            resource_controller = ResourceController(
                main_window.resource_panel,
                member_service,
                http_client_enhanced
            )
            controllers.append(resource_controller)
            print("✅ ResourceController 創建成功")
        except Exception as e:
            print(f"❌ ResourceController 創建失敗: {e}")
        
        try:
            from controllers.account_controller import AccountController
            account_controller = AccountController(
                main_window.account_panel,
                member_service,
                agent_service
            )
            controllers.append(account_controller)
            print("✅ AccountController 創建成功")
        except Exception as e:
            print(f"❌ AccountController 創建失敗: {e}")
        
        try:
            from controllers.rng_controller import RNGController
            rng_controller = RNGController(
                main_window.rng_panel,
                member_service
            )
            controllers.append(rng_controller)
            print("✅ RNGController 創建成功")
        except Exception as e:
            print(f"❌ RNGController 創建失敗: {e}")
        
        print(f"✅ 成功創建 {len(controllers)} 個控制器")
        
        # 關閉視窗
        root.after(1000, root.quit)
        root.mainloop()
        root.destroy()
        
        return len(controllers) > 0
        
    except Exception as e:
        print(f"❌ 控制器測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_connection():
    """測試 API 連接"""
    print("\n🔍 測試 API 連接...")
    
    try:
        from utils.environment_config import env_config
        import requests
        
        # 取得當前環境配置
        current_env = env_config.get_current_environment()
        env_info = env_config.get_environment_info(current_env)
        api_servers = env_info.get("api_servers", {})
        
        print(f"當前環境: {current_env}")
        print(f"API 服務器: {list(api_servers.keys())}")
        
        # 測試連接
        success_count = 0
        for service, url in api_servers.items():
            try:
                response = requests.get(url, timeout=5)
                print(f"✅ {service}: 連接成功 ({response.status_code})")
                success_count += 1
            except Exception as e:
                print(f"❌ {service}: 連接失敗 - {str(e)[:50]}")
        
        print(f"✅ {success_count}/{len(api_servers)} 個服務連接成功")
        return success_count > 0
        
    except Exception as e:
        print(f"❌ API 連接測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("VP Test Tool 修復測試")
    print("=" * 50)
    
    tests = [
        ("模組導入", test_imports),
        ("視窗創建", test_window_creation),
        ("控制器創建", test_controller_creation),
        ("API 連接", test_api_connection),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 執行測試: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 測試 {test_name} 異常: {e}")
            results.append((test_name, False))
    
    # 顯示測試結果
    print("\n" + "=" * 50)
    print("📊 測試結果總結:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 總計: {passed}/{len(results)} 個測試通過")
    
    if passed == len(results):
        print("🎉 所有測試通過！修復成功！")
    elif passed > 0:
        print("⚠️ 部分測試通過，需要進一步檢查")
    else:
        print("❌ 所有測試失敗，需要修復")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
