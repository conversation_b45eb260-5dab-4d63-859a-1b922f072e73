#!/usr/bin/env python3
"""
VP Test Tool - 穩定版 cx_Freeze 打包腳本

此腳本經過完整測試，包含所有必要的依賴模組，
避免常見的打包失敗問題。

使用方法:
    python setup_cx_freeze_stable.py build
    python setup_cx_freeze_stable.py build_exe

版本: V2.6.3
作者: VP Test Tool 開發團隊
日期: 2025-05-28
"""

import sys
import os
import shutil
from pathlib import Path
from cx_Freeze import setup, Executable

# 從版本文件獲取版本信息
try:
    from utils.version import VERSION, APP_TITLE, RELEASE_DATE
    APP_VERSION = VERSION
    print(f"📦 正在打包 {APP_TITLE}")
    print(f"📅 發布日期: {RELEASE_DATE}")
except ImportError:
    APP_VERSION = "2.6.3"
    APP_TITLE = "VP Test Tool V2.6.3"
    print("⚠️ 無法導入版本信息，使用預設版本")

print(f"🔧 使用 cx_Freeze 打包版本: {APP_VERSION}")
print("=" * 60)

# 檢查必要文件
required_files = ["main.py", "utils/version.py", "config.json"]
missing_files = [f for f in required_files if not Path(f).exists()]
if missing_files:
    print(f"❌ 缺少必要文件: {missing_files}")
    sys.exit(1)

# 清理舊的打包文件
def clean_build_dirs():
    """清理舊的打包目錄"""
    dirs_to_clean = ["build", "dist"]
    for dir_name in dirs_to_clean:
        if Path(dir_name).exists():
            print(f"🧹 清理舊目錄: {dir_name}")
            shutil.rmtree(dir_name)

# 包含的文件和目錄
include_files = [
    ("assets", "assets"),                    # 資源文件
    ("config.json", "config.json"),         # 配置文件
    ("CHANGELOG.md", "CHANGELOG.md"),       # 更新日誌
]

# 檢查可選文件
optional_files = [
    ("environments.json", "environments.json"),
    ("ip_templates.json", "ip_templates.json"),
    ("README.md", "README.md"),
]

for src, dst in optional_files:
    if Path(src).exists():
        include_files.append((src, dst))
        print(f"✅ 包含可選文件: {src}")

# 核心 Python 模組 - 經過測試的穩定配置
core_packages = [
    # GUI 框架
    "tkinter",
    "tkinter.ttk",
    "tkinter.messagebox",
    "tkinter.filedialog",

    # 圖像處理
    "PIL",
    "PIL.Image",
    "PIL.ImageTk",

    # 應用模組
    "utils",
    "views",
    "models",
    "controllers",
    "widgets",
]

# 網路和 HTTP 模組 - 完整的網路支援
network_packages = [
    "http",
    "http.client",
    "http.server",
    "urllib",
    "urllib.parse",
    "urllib.request",
    "urllib.error",
    "urllib3",
    "requests",
    "ssl",
    "socket",
    "certifi",
    "chardet",
    "charset_normalizer",
    "idna",
]

# 數據處理模組
data_packages = [
    "json",
    "csv",
    "datetime",
    "decimal",
    "pandas",
    "numpy",
    "openpyxl",
    "xlrd",
]

# 系統和工具模組
system_packages = [
    "os",
    "sys",
    "logging",
    "threading",
    "queue",
    "traceback",
    "subprocess",
    "pathlib",
    "shutil",
    "tempfile",
    "psutil",
    "platform",
    "time",
    "re",
    "hashlib",
    "base64",
    "uuid",
    "collections",
    "itertools",
    "functools",
    "weakref",
]

# 郵件和編碼模組
encoding_packages = [
    "email",
    "email.mime",
    "email.utils",
    "encodings",
    "encodings.utf_8",
    "encodings.cp1252",
    "encodings.ascii",
    "locale",
]

# 可選模組包 - 減少缺少模組警告 (只包含確實存在的模組)
optional_packages = [
    # XML 處理
    "xml",
    "xml.etree",
    "xml.parsers",
    "xml.sax",
    "xml.dom",
    "xmlrpc",
    "xmlrpc.client",

    # 壓縮和歸檔
    "zipfile",
    "zipimport",
    "zlib",
    "gzip",
    "bz2",
    "lzma",
    "tarfile",

    # 數據格式
    "pickle",
    "shelve",
    "sqlite3",
    "configparser",

    # 文本處理
    "string",
    "textwrap",
    "unicodedata",
    "stringprep",

    # 數學和統計
    "math",
    "cmath",
    "decimal",
    "fractions",
    "random",
    "statistics",

    # 日期和時間
    "calendar",
    "time",
    "datetime",

    # 文件和目錄
    "glob",
    "fnmatch",
    "linecache",
    "fileinput",
    "filecmp",
    "tempfile",
    "shutil",
    "ntpath",

    # 進程和線程
    "multiprocessing",
    "concurrent",
    "concurrent.futures",
    "_thread",

    # 網路和 URL
    "urllib",
    "urllib.parse",
    "urllib.request",
    "urllib.response",
    "urllib.error",
    "urllib.robotparser",
    "http",
    "http.client",
    "http.server",
    "http.cookies",
    "http.cookiejar",

    # 加密和安全
    "hashlib",
    "hmac",
    "secrets",
    "ssl",

    # 系統和平台 (Windows 專用)
    "platform",
    "ctypes",
    "ctypes.util",
    "ctypes.wintypes",
    "msvcrt",
    "winreg",
    "winsound",

    # 其他實用工具
    "copy",
    "pickle",
    "copyreg",
    "types",
    "weakref",
    "gc",
    "inspect",
    "dis",
    "importlib",
    "importlib.util",
    "importlib.machinery",
    "pkgutil",
    "runpy",
    "ast",
    "symtable",
    "keyword",
    "token",
    "tokenize",
    "py_compile",
    "compileall",
    "code",
    "codeop",
]

# 合併所有包
packages = (core_packages + network_packages + data_packages +
           system_packages + encoding_packages + optional_packages)

# 明確包含的模組
includes = [
    # tkinter 相關
    "tkinter.constants",
    "tkinter.dnd",
    "tkinter.filedialog",
    "tkinter.messagebox",
    "tkinter.simpledialog",
    "tkinter.colorchooser",
    "tkinter.font",
    "tkinter.ttk",

    # PIL 相關
    "PIL._imaging",
    "PIL._imagingcms",
    "PIL._imagingft",
    "PIL._imagingmath",
    "PIL._imagingmorph",
    "PIL._imagingtk",
    "PIL._webp",

    # numpy 相關
    "numpy.core._multiarray_umath",
    "numpy.core._multiarray_tests",
    "numpy.core._operand_flag_tests",
    "numpy.core._rational_tests",
    "numpy.core._simd",
    "numpy.core._struct_ufunc_tests",
    "numpy.core._umath_tests",
    "numpy.fft._pocketfft_internal",
    "numpy.linalg._umath_linalg",
    "numpy.linalg.lapack_lite",
    "numpy.random._bounded_integers",
    "numpy.random._common",
    "numpy.random._generator",
    "numpy.random._mt19937",
    "numpy.random._pcg64",
    "numpy.random._philox",
    "numpy.random._sfc64",
    "numpy.random.bit_generator",
    "numpy.random.mtrand",

    # pandas 相關
    "pandas._libs.algos",
    "pandas._libs.arrays",
    "pandas._libs.byteswap",
    "pandas._libs.groupby",
    "pandas._libs.hashing",
    "pandas._libs.hashtable",
    "pandas._libs.index",
    "pandas._libs.indexing",
    "pandas._libs.internals",
    "pandas._libs.interval",
    "pandas._libs.join",
    "pandas._libs.json",
    "pandas._libs.lib",
    "pandas._libs.missing",
    "pandas._libs.ops",
    "pandas._libs.ops_dispatch",
    "pandas._libs.pandas_datetime",
    "pandas._libs.pandas_parser",
    "pandas._libs.parsers",
    "pandas._libs.properties",
    "pandas._libs.reshape",
    "pandas._libs.sas",
    "pandas._libs.sparse",
    "pandas._libs.testing",
    "pandas._libs.tslib",
    "pandas._libs.tslibs.base",
    "pandas._libs.tslibs.ccalendar",
    "pandas._libs.tslibs.conversion",
    "pandas._libs.tslibs.dtypes",
    "pandas._libs.tslibs.fields",
    "pandas._libs.tslibs.nattype",
    "pandas._libs.tslibs.np_datetime",
    "pandas._libs.tslibs.offsets",
    "pandas._libs.tslibs.parsing",
    "pandas._libs.tslibs.period",
    "pandas._libs.tslibs.strptime",
    "pandas._libs.tslibs.timedeltas",
    "pandas._libs.tslibs.timestamps",
    "pandas._libs.tslibs.timezones",
    "pandas._libs.tslibs.tzconversion",
    "pandas._libs.tslibs.vectorized",
    "pandas._libs.window.aggregations",
    "pandas._libs.window.indexers",
    "pandas._libs.writers",

    # psutil 相關
    "psutil._psutil_windows",

    # charset_normalizer 相關
    "charset_normalizer.md",
    "charset_normalizer.md__mypyc",

    # zstandard 相關
    "zstandard._cffi",
    "zstandard.backend_c",

    # 系統模組
    "pyexpat",
    "select",
    "unicodedata",
    "_bz2",
    "_ctypes",
    "_decimal",
    "_elementtree",
    "_hashlib",
    "_lzma",
    "_multiprocessing",
    "_queue",
    "_socket",
    "_sqlite3",
    "_ssl",
    "_tkinter",
    "_uuid",

    # XML 相關
    "xml.etree.ElementTree",
    "xml.etree.ElementPath",
    "xml.parsers.expat",
    "xml.sax",
    "xml.sax.handler",
    "xml.sax.saxutils",
    "xml.sax.xmlreader",
    "xml.sax.expatreader",
    "xml.sax._exceptions",
    "xml.dom.minidom",
    "xml.dom.expatbuilder",
    "xml.dom.minicompat",
    "xml.dom.pulldom",
    "xml.dom.xmlbuilder",
    "xml.dom.NodeFilter",
    "xml.dom.domreg",

    # 其他重要模組
    "zipfile",
    "zipimport",
    "zlib",
    "email.mime.text",
    "email.mime.multipart",
    "email.mime.base",
    "email.encoders",
    "email.header",
    "email.charset",
    "email.message",
    "email.parser",
    "email.generator",
    "email.iterators",
    "email.policy",
    "email.contentmanager",
    "email.errors",
    "email._parseaddr",
    "email._policybase",
    "email.utils",
]

# 排除的模組 - 只排除確定不需要的大型模組
excludes = [
    # 測試和開發工具 (保留基本的 unittest，移除其他)
    "test",
    "tests",
    "pytest",
    "nose",

    # 不需要的 GUI 框架
    "PyQt4",
    "PyQt5",
    "PyQt6",
    "PySide",
    "PySide2",
    "PySide6",
    "wx",
    "AppKit",
    "Foundation",

    # 科學計算（如果不需要）
    "scipy",
    "matplotlib",
    "sympy",
    "sklearn",
    "numba",
    "numexpr",

    # 開發工具
    "IPython",
    "jupyter",
    "notebook",
    "spyder",

    # 編譯工具
    "distutils",
    "setuptools",
    "pip",

    # 其他大型庫
    "tornado",
    "django",
    "flask",
    "sqlalchemy",
    "tables",
    "pyarrow",
    "fsspec",
    "botocore",
    "google.auth",
    "hypothesis",
    "lxml",
    "bs4",
    "defusedxml",
    "markupsafe",
    "cryptography",
    "brotli",
    "brotlicffi",
    "OpenSSL",
    "pyxlsb",
    "python_calamine",
    "xlsxwriter",
    "odf",
    "qtpy",
    "traitlets",
    "simplejson",
    "socks",
    "urllib3_secure_extra",
    "win32api",
    "win32con",
    "win32evtlog",
    "win32evtlogutil",
    "win32process",
    "wmi",
    "_curses",
    "resource",
]

# 構建選項
build_options = {
    "packages": packages,
    "includes": includes,
    "excludes": excludes,
    "include_files": include_files,
    "build_exe": "dist/cx_freeze_stable",
    "optimize": 1,                    # 適中的優化級別
    "include_msvcr": True,           # 包含 MSVC 運行時
    "replace_paths": [("*", "")],    # 移除路徑信息
    "zip_include_packages": ["*"],   # 壓縮包以減少文件數量
    "zip_exclude_packages": [],      # 不排除任何包的壓縮
}

# 可執行文件配置
base = None
if sys.platform == "win32":
    base = "Win32GUI"  # Windows GUI 應用

# 主程式配置
executables = [
    Executable(
        script="main.py",
        base=base,
        target_name="VP_Test_Tool.exe",
        icon="assets/icons/vp_test_tool.ico" if Path("assets/icons/vp_test_tool.ico").exists() else None,
        copyright=f"© 2025 VP Test Tool 開發團隊",
        trademarks="VP Test Tool",
    )
]

# 打包前檢查
def pre_build_check():
    """打包前的檢查"""
    print("🔍 執行打包前檢查...")

    # 檢查 Python 版本
    if sys.version_info < (3, 8):
        print("❌ 需要 Python 3.8 或更高版本")
        return False

    # 檢查 cx_Freeze
    try:
        import cx_Freeze
        try:
            version = cx_Freeze.__version__
        except AttributeError:
            version = "已安裝"
        print(f"✅ cx_Freeze 版本: {version}")
    except ImportError:
        print("❌ 未安裝 cx_Freeze")
        return False

    # 檢查關鍵依賴
    critical_deps = ["tkinter", "PIL", "requests", "pandas", "numpy"]
    missing_deps = []

    for dep in critical_deps:
        try:
            __import__(dep)
            print(f"✅ {dep}: 已安裝")
        except ImportError:
            missing_deps.append(dep)
            print(f"❌ {dep}: 未安裝")

    if missing_deps:
        print(f"❌ 缺少關鍵依賴: {missing_deps}")
        return False

    print("✅ 打包前檢查通過")
    return True

# 打包後驗證
def post_build_verify():
    """打包後驗證"""
    print("\n🔍 執行打包後驗證...")

    build_dir = Path("dist/cx_freeze_stable")
    if not build_dir.exists():
        print("❌ 打包目錄不存在")
        return False

    # 檢查主程式
    exe_path = build_dir / "VP_Test_Tool.exe"
    if not exe_path.exists():
        print("❌ 主程式不存在")
        return False

    print(f"✅ 主程式: {exe_path.stat().st_size:,} bytes")

    # 檢查關鍵文件
    critical_files = ["python311.dll", "config.json"]
    for file_name in critical_files:
        file_path = build_dir / file_name
        if file_path.exists():
            print(f"✅ {file_name}: {file_path.stat().st_size:,} bytes")
        else:
            print(f"❌ 缺少: {file_name}")
            return False

    # 統計信息
    total_files = len(list(build_dir.rglob("*")))
    total_size = sum(f.stat().st_size for f in build_dir.rglob("*") if f.is_file())

    print(f"📊 總文件數: {total_files:,}")
    print(f"📊 總大小: {total_size / (1024*1024):.1f} MB")
    print("✅ 打包後驗證通過")

    return True

# 主函數
def main():
    """主打包函數"""
    print(f"🚀 開始打包 VP Test Tool V{APP_VERSION}")

    # 打包前檢查
    if not pre_build_check():
        print("❌ 打包前檢查失敗")
        sys.exit(1)

    # 清理舊文件
    clean_build_dirs()

    # 執行打包
    try:
        print("\n📦 開始 cx_Freeze 打包...")
        setup(
            name="VP_Test_Tool",
            version=APP_VERSION,
            description="VP Test Tool - 遊戲測試工具",
            author="VP Test Tool 開發團隊",
            options={"build_exe": build_options},
            executables=executables,
        )

        print("✅ cx_Freeze 打包完成")

        # 打包後驗證
        if post_build_verify():
            print(f"\n🎉 VP Test Tool V{APP_VERSION} 打包成功！")
            print(f"📁 輸出目錄: dist/cx_freeze_stable/")
            print(f"🎯 主程式: dist/cx_freeze_stable/VP_Test_Tool.exe")
            print("\n💡 使用建議:")
            print("   1. 將整個 dist/cx_freeze_stable 目錄分發給用戶")
            print("   2. 確保目標系統安裝了 Visual C++ 運行時")
            print("   3. 建議將程式加入防毒軟體白名單")
        else:
            print("❌ 打包後驗證失敗")
            sys.exit(1)

    except Exception as e:
        print(f"❌ 打包失敗: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
