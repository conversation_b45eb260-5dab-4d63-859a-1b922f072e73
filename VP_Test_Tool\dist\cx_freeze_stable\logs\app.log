2025-05-28 15:43:23,884 - INFO - 5108 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-28 15:43:24,026 - INFO - 5108 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-28 15:43:24,026 - INFO - 5108 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-28 15:43:24,026 - INFO - 5108 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-28 15:43:24,032 - INFO - 5108 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 38052.21 MB, 使用率 41.7%
2025-05-28 15:43:24,032 - INFO - 5108 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 39.85 GB, 使用率 95.7%
2025-05-28 15:43:24,096 - INFO - 5108 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-28 15:43:24,152 - INFO - 5108 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-28 15:43:24,214 - INFO - 5108 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-28 15:43:24,345 - INFO - 5108 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-28 15:43:30,696 - INFO - 5108 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-28 15:43:30,781 - INFO - 5108 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-28 15:43:30,837 - INFO - 5108 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-28 15:43:30,838 - INFO - 5108 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-28 15:43:30,907 - INFO - 5108 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-28 15:43:30,907 - INFO - 5108 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-28 15:43:32,328 - INFO - 5108 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-28 15:43:32,328 - INFO - 5108 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-28 15:43:32,669 - INFO - 5108 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-28 15:43:32,670 - INFO - 5108 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-28 15:43:32,735 - INFO - 5108 - MainThread - app - enhanced_logger.py:234 - 成功初始化 API IP 切換工具控制器
2025-05-28 15:43:32,787 - INFO - 5108 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-28 16:19:19,345 - INFO - 5108 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-05-28 16:19:19,364 - INFO - 5108 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-05-28 16:19:19,364 - WARNING - 5108 - MainThread - app - enhanced_logger.py:245 - 初始化鍵盤快捷鍵管理器失敗: can't invoke "winfo" command: application has been destroyed
2025-05-28 16:19:19,365 - INFO - 5108 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-05-28 16:19:19,370 - WARNING - 5108 - MainThread - app - enhanced_logger.py:245 - 顯示歡迎訊息失敗: invalid command name ".!label"
2025-05-28 16:19:19,370 - WARNING - 5108 - MainThread - app - enhanced_logger.py:245 - 添加功能檢測按鈕失敗: can't invoke "winfo" command: application has been destroyed
2025-05-28 16:19:19,371 - ERROR - 5108 - MainThread - app - enhanced_logger.py:278 - 檢查根視窗是否有效時發生異常: can't invoke "winfo" command: application has been destroyed
Traceback (most recent call last):
  File "main.py", line 760, in main
  File "tkinter\__init__.py", line 1139, in winfo_exists
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
2025-05-28 17:35:30,629 - INFO - 39404 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-28 17:35:30,770 - INFO - 39404 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-28 17:35:30,770 - INFO - 39404 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-28 17:35:30,770 - INFO - 39404 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-28 17:35:30,775 - INFO - 39404 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 38949.00 MB, 使用率 40.4%
2025-05-28 17:35:30,775 - INFO - 39404 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 39.85 GB, 使用率 95.7%
2025-05-28 17:35:30,839 - INFO - 39404 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-28 17:35:30,896 - INFO - 39404 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-28 17:35:30,959 - INFO - 39404 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-28 17:35:31,269 - INFO - 39404 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-28 17:35:37,854 - INFO - 39404 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-28 17:35:37,920 - INFO - 39404 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-28 17:35:37,994 - INFO - 39404 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-28 17:35:37,994 - INFO - 39404 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-28 17:35:38,046 - INFO - 39404 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-28 17:35:38,046 - INFO - 39404 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-28 17:35:39,474 - INFO - 39404 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-28 17:35:39,475 - INFO - 39404 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-28 17:35:39,807 - INFO - 39404 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-28 17:35:39,808 - INFO - 39404 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-28 17:35:39,919 - INFO - 39404 - MainThread - app - enhanced_logger.py:234 - 成功初始化 API IP 切換工具控制器
2025-05-28 17:35:39,979 - INFO - 39404 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-28 17:36:27,684 - INFO - 39404 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-05-28 17:36:27,696 - INFO - 39404 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-05-28 17:36:27,697 - WARNING - 39404 - MainThread - app - enhanced_logger.py:245 - 初始化鍵盤快捷鍵管理器失敗: can't invoke "winfo" command: application has been destroyed
2025-05-28 17:36:27,697 - INFO - 39404 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-05-28 17:36:27,700 - WARNING - 39404 - MainThread - app - enhanced_logger.py:245 - 顯示歡迎訊息失敗: invalid command name ".!label"
2025-05-28 17:36:27,700 - WARNING - 39404 - MainThread - app - enhanced_logger.py:245 - 添加功能檢測按鈕失敗: can't invoke "winfo" command: application has been destroyed
2025-05-28 17:36:27,701 - ERROR - 39404 - MainThread - app - enhanced_logger.py:278 - 檢查根視窗是否有效時發生異常: can't invoke "winfo" command: application has been destroyed
Traceback (most recent call last):
  File "main.py", line 760, in main
  File "tkinter\__init__.py", line 1139, in winfo_exists
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
