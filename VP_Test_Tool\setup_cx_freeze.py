"""
使用 cx_Freeze 打包 VP Test Tool 為 exe 文件
cx_Freeze 創建包含 Python 解釋器的獨立可執行文件
這可能會減少被防毒軟體誤判為病毒的機率
"""
import sys
import os
from cx_Freeze import setup, Executable

# 應用程式信息
APP_NAME = "VP_Test_Tool"
APP_VERSION = "2.6.3"
APP_DESCRIPTION = "VP Test Tool"
APP_AUTHOR = "VP Test Tool Team"

# 圖示路徑
icon_path = os.path.abspath(os.path.join("assets", "icons", "vp_test_tool.ico"))
if not os.path.exists(icon_path):
    print(f"Warning: Icon file not found at {icon_path}")
    icon_path = None

# 基本設置
base = None
if sys.platform == "win32":
    base = "Win32GUI"  # 使用 GUI 模式，不顯示控制台窗口

# 包含的文件和目錄
include_files = [
    ("assets", "assets"),  # 包含資源文件
    ("config.json", "config.json"),  # 包含配置文件
    ("CHANGELOG.md", "CHANGELOG.md"),  # 包含更新日誌
]

# 包含的包
packages = [
    "tkinter",
    "PIL",
    "utils",
    "views",
    "models",
    "controllers",
    "widgets",
    "json",
    "logging",
    "requests",
    "urllib3",
    "threading",
    "queue",
    "datetime",
    "os",
    "sys",
    "traceback",
    "http",
    "http.client",
    "urllib",
    "urllib.parse",
    "urllib.request",
    "ssl",
    "socket",
    "email",
    "certifi",
    "chardet",
    "charset_normalizer",
    "idna",
]

# 包含的模組
includes = [
    "tkinter.ttk",
    "tkinter.messagebox",
    "tkinter.filedialog",
    "PIL.Image",
    "PIL.ImageTk",
]

# 排除的包
excludes = [
    "unittest",
    "html",
    "xml",
    "pydoc",
    "test",
    "distutils",
    "scipy",
    "matplotlib",
    "PyQt5",
    "PyQt6",
    "PySide2",
    "PySide6",
    "IPython",
    "jupyter",
    "notebook",
]

# 構建選項
build_options = {
    "packages": packages,
    "includes": includes,
    "excludes": excludes,
    "include_files": include_files,
    "build_exe": "dist/cx_freeze",  # 輸出目錄
    "optimize": 1,  # 降低優化級別，避免某些模組被移除
    "include_msvcr": True,  # 包含 MSVC 運行時
}

# 可執行文件選項
executables = [
    Executable(
        "main.py",  # 主程序文件
        base=base,
        target_name=APP_NAME,  # 輸出文件名
        icon=icon_path,  # 圖示
        copyright=f"Copyright (c) {APP_AUTHOR}",  # 版權信息
    )
]

# 設置
setup(
    name=APP_NAME,
    version=APP_VERSION,
    description=APP_DESCRIPTION,
    author=APP_AUTHOR,
    options={"build_exe": build_options},
    executables=executables
)

print(f"打包完成！exe 文件位於: {os.path.abspath(os.path.join('dist', 'cx_freeze', APP_NAME + '.exe'))}")
