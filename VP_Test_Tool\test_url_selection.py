#!/usr/bin/env python3
"""
測試 URL 配置編輯的選擇效果
驗證點選是否有明顯的圈選呈現
"""

import sys
import os
import tkinter as tk
from tkinter import ttk

# 添加專案根目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_url_selection_ui():
    """測試 URL 選擇 UI"""
    print("=== 測試 URL 配置編輯選擇效果 ===")
    
    try:
        # 創建測試視窗
        root = tk.Tk()
        root.title("URL 配置編輯選擇測試")
        root.geometry("1000x700")
        
        # 導入 IP 切換面板
        from views.ip_switcher_panel import IPSwitcherPanel
        
        print("🔍 創建 IP 切換面板...")
        ip_panel = IPSwitcherPanel(root)
        ip_panel.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 添加測試數據到 URL 列表
        test_urls = {
            "mysql_operator": "http://************:5000",
            "gamebridge": "http://gamebridge:8080",
            "tokenguard": "http://tokenguard:8080",
            "lottery": "http://lottery:8080",
            "simulation": "http://simulation:8080"
        }
        
        print("🔍 添加測試數據...")
        ip_panel.update_url_list(test_urls)
        
        # 添加測試歷史數據
        test_history = [
            {
                "timestamp": "2025-05-28T10:30:00Z",
                "operation": "快速 IP 切換",
                "server_config": {
                    "mysql_operator": "http://************:5000",
                    "gamebridge": "http://gamebridge:8080"
                }
            },
            {
                "timestamp": "2025-05-28T09:15:00Z",
                "operation": "應用模板",
                "server_config": {
                    "mysql_operator": "http://************:5000",
                    "tokenguard": "http://tokenguard:8080"
                }
            }
        ]
        
        print("🔍 添加測試歷史數據...")
        ip_panel.update_history_list(test_history)
        
        # 檢查 Treeview 樣式
        print("\n檢查 Treeview 樣式配置:")
        
        # 檢查 URL Treeview
        url_tree_style = ip_panel.url_tree.cget("style")
        print(f"✅ URL Treeview 樣式: {url_tree_style}")
        
        # 檢查歷史 Treeview
        history_tree_style = ip_panel.history_tree.cget("style")
        print(f"✅ 歷史 Treeview 樣式: {history_tree_style}")
        
        # 檢查選擇模式
        url_selectmode = ip_panel.url_tree.cget("selectmode")
        history_selectmode = ip_panel.history_tree.cget("selectmode")
        print(f"✅ URL Treeview 選擇模式: {url_selectmode}")
        print(f"✅ 歷史 Treeview 選擇模式: {history_selectmode}")
        
        # 添加說明標籤
        info_frame = tk.Frame(root)
        info_frame.pack(fill=tk.X, padx=20, pady=10)
        
        info_label = tk.Label(
            info_frame,
            text="測試說明：\n"
                 "1. 點擊 URL 配置編輯區域的任一項目，應該看到明顯的藍色選中效果\n"
                 "2. 點擊切換歷史區域的任一項目，應該看到明顯的藍色選中效果\n"
                 "3. 右鍵點擊 URL 項目應該顯示右鍵菜單\n"
                 "4. 雙擊 URL 項目應該觸發編輯功能",
            font=("Microsoft JhengHei UI", 10),
            fg="blue",
            justify=tk.LEFT
        )
        info_label.pack(anchor=tk.W)
        
        # 添加測試按鈕
        button_frame = tk.Frame(root)
        button_frame.pack(fill=tk.X, padx=20, pady=10)
        
        def test_programmatic_selection():
            """程式化選擇測試"""
            try:
                # 選擇第一個 URL 項目
                children = ip_panel.url_tree.get_children()
                if children:
                    ip_panel.url_tree.selection_set(children[0])
                    ip_panel.url_tree.focus(children[0])
                    print("✅ 程式化選擇第一個 URL 項目")
                
                # 選擇第一個歷史項目
                history_children = ip_panel.history_tree.get_children()
                if history_children:
                    ip_panel.history_tree.selection_set(history_children[0])
                    ip_panel.history_tree.focus(history_children[0])
                    print("✅ 程式化選擇第一個歷史項目")
                    
            except Exception as e:
                print(f"❌ 程式化選擇測試失敗: {e}")
        
        def clear_selections():
            """清除所有選擇"""
            try:
                ip_panel.url_tree.selection_remove(ip_panel.url_tree.selection())
                ip_panel.history_tree.selection_remove(ip_panel.history_tree.selection())
                print("✅ 已清除所有選擇")
            except Exception as e:
                print(f"❌ 清除選擇失敗: {e}")
        
        # 測試按鈕
        test_select_btn = tk.Button(
            button_frame,
            text="測試程式化選擇",
            command=test_programmatic_selection,
            bg="#0D47A1",
            fg="#FFFFFF",
            font=("Microsoft JhengHei UI", 10, "bold"),
            relief=tk.RAISED,
            borderwidth=2,
            padx=15,
            pady=5,
            cursor="hand2"
        )
        test_select_btn.pack(side=tk.LEFT, padx=5)
        
        clear_btn = tk.Button(
            button_frame,
            text="清除選擇",
            command=clear_selections,
            bg="#424242",
            fg="#FFFFFF",
            font=("Microsoft JhengHei UI", 10, "bold"),
            relief=tk.RAISED,
            borderwidth=2,
            padx=15,
            pady=5,
            cursor="hand2"
        )
        clear_btn.pack(side=tk.LEFT, padx=5)
        
        close_btn = tk.Button(
            button_frame,
            text="關閉測試",
            command=root.destroy,
            bg="#B71C1C",
            fg="#FFFFFF",
            font=("Microsoft JhengHei UI", 10, "bold"),
            relief=tk.RAISED,
            borderwidth=2,
            padx=15,
            pady=5,
            cursor="hand2"
        )
        close_btn.pack(side=tk.RIGHT, padx=5)
        
        print("\n✅ URL 選擇測試界面創建成功")
        print("請手動測試以下功能:")
        print("1. 點擊 URL 配置編輯區域的項目")
        print("2. 點擊切換歷史區域的項目")
        print("3. 右鍵點擊 URL 項目")
        print("4. 雙擊 URL 項目")
        print("5. 使用測試按鈕進行程式化選擇")
        
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_treeview_styles():
    """測試 Treeview 樣式配置"""
    print("\n=== 測試 Treeview 樣式配置 ===")
    
    try:
        # 創建測試視窗
        root = tk.Tk()
        root.title("Treeview 樣式測試")
        root.geometry("800x600")
        
        # 創建樣式
        style = ttk.Style()
        
        # 配置增強樣式
        style.configure("Enhanced.Treeview",
                      background="#FFFFFF",
                      foreground="#000000",
                      fieldbackground="#FFFFFF",
                      font=("Microsoft JhengHei UI", 10))
        
        style.map("Enhanced.Treeview",
                 background=[('selected', '#0078D4'),
                            ('focus', '#E3F2FD')],
                 foreground=[('selected', '#FFFFFF'),
                            ('focus', '#000000')])
        
        # 創建測試 Treeview
        frame = ttk.Frame(root)
        frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 標準樣式 Treeview
        label1 = tk.Label(frame, text="標準樣式 Treeview", font=("Microsoft JhengHei UI", 12, "bold"))
        label1.pack(anchor=tk.W, pady=(0, 5))
        
        tree1 = ttk.Treeview(frame, columns=("col1", "col2"), show="headings", height=4)
        tree1.heading("col1", text="服務名稱")
        tree1.heading("col2", text="URL 地址")
        tree1.pack(fill=tk.X, pady=(0, 20))
        
        # 增強樣式 Treeview
        label2 = tk.Label(frame, text="增強樣式 Treeview", font=("Microsoft JhengHei UI", 12, "bold"))
        label2.pack(anchor=tk.W, pady=(0, 5))
        
        tree2 = ttk.Treeview(frame, columns=("col1", "col2"), show="headings", height=4, style="Enhanced.Treeview")
        tree2.heading("col1", text="服務名稱")
        tree2.heading("col2", text="URL 地址")
        tree2.pack(fill=tk.X, pady=(0, 20))
        
        # 添加測試數據
        test_data = [
            ("mysql_operator", "http://************:5000"),
            ("gamebridge", "http://gamebridge:8080"),
            ("tokenguard", "http://tokenguard:8080")
        ]
        
        for data in test_data:
            tree1.insert("", tk.END, values=data)
            tree2.insert("", tk.END, values=data)
        
        # 說明標籤
        info_label = tk.Label(
            frame,
            text="比較兩個 Treeview 的選擇效果：\n"
                 "上方是標準樣式，下方是增強樣式\n"
                 "點擊項目查看選中效果的差異",
            font=("Microsoft JhengHei UI", 10),
            fg="blue",
            justify=tk.LEFT
        )
        info_label.pack(anchor=tk.W, pady=10)
        
        # 關閉按鈕
        close_btn = tk.Button(
            frame,
            text="關閉測試",
            command=root.destroy,
            bg="#B71C1C",
            fg="#FFFFFF",
            font=("Microsoft JhengHei UI", 10, "bold"),
            relief=tk.RAISED,
            borderwidth=2,
            padx=15,
            pady=5,
            cursor="hand2"
        )
        close_btn.pack(pady=10)
        
        print("✅ Treeview 樣式測試界面創建成功")
        print("請比較兩個 Treeview 的選擇效果")
        
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主測試函數"""
    print("URL 配置編輯選擇效果測試開始")
    print("=" * 50)
    
    tests = [
        ("URL 選擇 UI 測試", test_url_selection_ui),
        ("Treeview 樣式測試", test_treeview_styles)
    ]
    
    passed = 0
    total = len(tests)
    
    for i, (test_name, test_func) in enumerate(tests):
        print(f"\n執行測試 {i+1}/{total}: {test_name}")
        print("-" * 40)
        try:
            if test_func():
                passed += 1
                print(f"✅ 測試 {test_name} 通過")
            else:
                print(f"❌ 測試 {test_name} 失敗")
        except Exception as e:
            print(f"❌ 測試 {test_name} 執行異常: {e}")
    
    print("\n" + "=" * 50)
    print(f"測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有 URL 選擇效果測試通過！")
        print("\n主要改進:")
        print("✅ URL Treeview 使用增強樣式")
        print("✅ 選中時有明顯的藍色背景")
        print("✅ 支援多選模式")
        print("✅ 添加右鍵菜單功能")
        print("✅ 添加複製功能")
        print("✅ 按鈕狀態根據選擇動態更新")
        return 0
    else:
        print("⚠️ 部分測試未通過，請檢查相關功能。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
